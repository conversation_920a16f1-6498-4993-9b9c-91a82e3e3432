'use client';

import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { ProtectedRoute } from '@/contexts/AuthContext';
import { useListData, useFormSubmit, useModal, useForm, useFetch } from '@/lib/hooks';
import { productAPI, brandAPI, categoryAPI } from '@/lib/api';
import { formatDate, truncateText, formatCurrency, getInputClassName } from '@/lib/utils';
import ImageUpload from '@/components/ImageUpload';
import QRCodeGenerator from '@/components/QRCodeGenerator';

interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  brand_id: number;
  category_id: number;
  brand_name?: string;
  category_name?: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
}

interface ProductForm {
  name: string;
  description: string;
  price: string;
  brand_id: string;
  category_id: string;
  image_url: string;
}

function ProductModal({ isOpen, onClose, product, onSuccess }: {
  isOpen: boolean;
  onClose: () => void;
  product?: Product;
  onSuccess: () => void;
}) {
  const isEdit = !!product;
  const { loading, error, success, submit, reset } = useFormSubmit();
  const { data: brands } = useFetch(() => brandAPI.getAllBrands());
  const { data: categories } = useFetch(() => categoryAPI.getAllCategories());
  const [uploadError, setUploadError] = useState<string | null>(null);
  
  const {
    values,
    errors,
    handleChange,
    setError,
    clearErrors,
    setValue,
    reset: resetForm
  } = useForm<ProductForm>({
    name: product?.name || '',
    description: product?.description || '',
    price: product?.price?.toString() || '',
    brand_id: product?.brand_id?.toString() || '',
    category_id: product?.category_id?.toString() || '',
    image_url: product?.image_url || ''
  });

  const validateForm = (): boolean => {
    clearErrors();
    let isValid = true;

    if (!values.name.trim()) {
      setError('name', 'Product name is required');
      isValid = false;
    }

    if (!values.price || isNaN(Number(values.price)) || Number(values.price) <= 0) {
      setError('price', 'Valid price is required');
      isValid = false;
    }

    if (!values.brand_id) {
      setError('brand_id', 'Brand is required');
      isValid = false;
    }

    if (!values.category_id) {
      setError('category_id', 'Category is required');
      isValid = false;
    }

    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const productData = {
      name: values.name,
      description: values.description,
      price: Number(values.price),
      brand_id: Number(values.brand_id),
      category_id: Number(values.category_id),
      image_url: values.image_url
    };

    const success = await submit(async () => {
      if (isEdit) {
        return productAPI.updateProduct(product.id, productData);
      } else {
        return productAPI.createProduct(productData);
      }
    });

    if (success) {
      onSuccess();
      onClose();
      resetForm();
      reset();
    }
  };

  const handleClose = () => {
    onClose();
    resetForm();
    reset();
    setUploadError(null);
  };

  const handleImageUploaded = (imageUrl: string) => {
    setValue('image_url', imageUrl);
    setUploadError(null);
  };

  const handleImageError = (error: string) => {
    setUploadError(error);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              {isEdit ? 'Edit Product' : 'Create New Product'}
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            )}

            {success && (
              <div className="bg-green-50 border border-green-200 rounded-md p-3">
                <p className="text-sm text-green-800">{success}</p>
              </div>
            )}

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Product Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={values.name}
                onChange={handleChange}
                className={getInputClassName(!!errors.name)}
                placeholder="Enter product name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={values.description}
                onChange={handleChange}
                className={getInputClassName()}
                placeholder="Enter product description (optional)"
              />
            </div>

            <div>
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                Price *
              </label>
              <input
                type="number"
                id="price"
                name="price"
                step="0.01"
                min="0"
                value={values.price}
                onChange={handleChange}
                className={getInputClassName(!!errors.price)}
                placeholder="0.00"
              />
              {errors.price && (
                <p className="mt-1 text-sm text-red-600">{errors.price}</p>
              )}
            </div>

            <div>
              <label htmlFor="brand_id" className="block text-sm font-medium text-gray-700 mb-1">
                Brand *
              </label>
              <select
                id="brand_id"
                name="brand_id"
                value={values.brand_id}
                onChange={handleChange}
                className={getInputClassName(!!errors.brand_id)}
              >
                <option value="">Select a brand</option>
                {brands?.map((brand: any) => (
                  <option key={brand.id} value={brand.id}>
                    {brand.name}
                  </option>
                ))}
              </select>
              {errors.brand_id && (
                <p className="mt-1 text-sm text-red-600">{errors.brand_id}</p>
              )}
            </div>

            <div>
              <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 mb-1">
                Category *
              </label>
              <select
                id="category_id"
                name="category_id"
                value={values.category_id}
                onChange={handleChange}
                className={getInputClassName(!!errors.category_id)}
              >
                <option value="">Select a category</option>
                {categories?.map((category: any) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category_id && (
                <p className="mt-1 text-sm text-red-600">{errors.category_id}</p>
              )}
            </div>

            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Image
              </label>
              {uploadError && (
                <div className="mb-2 bg-red-50 border border-red-200 rounded-md p-2">
                  <p className="text-sm text-red-800">{uploadError}</p>
                </div>
              )}
              {values.image_url && (
                <div className="mb-2">
                  <img
                    src={values.image_url}
                    alt="Product preview"
                    className="w-20 h-20 object-cover rounded-md border border-gray-300"
                  />
                </div>
              )}
              <ImageUpload
                onImageUploaded={handleImageUploaded}
                onError={handleImageError}
                className="mb-2"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                  loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]'
                }`}
              >
                {loading ? 'Saving...' : (isEdit ? 'Update' : 'Create')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function ProductsContent() {
  const { items: products, loading, error, refetch, removeItem } = useListData<Product>(
    () => productAPI.getAllProducts()
  );
  const { isOpen, data: selectedProduct, open, close } = useModal();
  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);

  const handleCreate = () => {
    open();
  };

  const handleEdit = (product: Product) => {
    open(product);
  };

  const handleDelete = async (product: Product) => {
    if (!confirm(`Are you sure you want to delete "${product.name}"?`)) return;

    setDeleteLoading(product.id);
    try {
      const response = await productAPI.deleteProduct(product.id);
      if (response.success) {
        removeItem(product.id);
      } else {
        alert(response.error || 'Failed to delete product');
      }
    } catch (error: any) {
      alert(error.response?.data?.error || 'Failed to delete product');
    } finally {
      setDeleteLoading(null);
    }
  };

  const handleModalSuccess = () => {
    refetch();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E6B120]"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Products</h1>
          <p className="text-gray-600">Manage your product catalog</p>
        </div>
        <button
          onClick={handleCreate}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
        >
          <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Product
        </button>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Products List */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        {products.length === 0 ? (
          <div className="p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No products</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating a new product.</p>
            <div className="mt-6">
              <button
                onClick={handleCreate}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Product
              </button>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {products.map((product) => (
              <div key={product.id} className="p-6 flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  {product.image_url && (
                    <img
                      src={product.image_url}
                      alt={product.name}
                      className="w-16 h-16 object-cover rounded-lg border border-gray-200"
                    />
                  )}
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-900">{product.name}</h3>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#E6B120] text-white">
                        {formatCurrency(product.price)}
                      </span>
                    </div>
                  {product.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {truncateText(product.description, 100)}
                    </p>
                  )}
                  <div className="flex items-center space-x-4 mt-2">
                    <span className="text-xs text-gray-400">
                      Brand: {product.brand_name || 'N/A'}
                    </span>
                    <span className="text-xs text-gray-400">
                      Category: {product.category_name || 'N/A'}
                    </span>
                    <span className="text-xs text-gray-400">
                      Created: {formatDate(product.created_at)}
                    </span>
                  </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <QRCodeGenerator
                    data={`Product: ${product.name}\nPrice: ${formatCurrency(product.price)}\nBrand: ${product.brand_name || 'N/A'}\nCategory: ${product.category_name || 'N/A'}\nID: ${product.id}`}
                    filename={`qr_${product.name.replace(/[^a-zA-Z0-9]/g, '_')}.png`}
                  />
                  <button
                    onClick={() => handleEdit(product)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(product)}
                    disabled={deleteLoading === product.id}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {deleteLoading === product.id ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal */}
      <ProductModal
        isOpen={isOpen}
        onClose={close}
        product={selectedProduct}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
}

export default function ProductsPage() {
  return (
    <ProtectedRoute>
      <AdminLayout>
        <ProductsContent />
      </AdminLayout>
    </ProtectedRoute>
  );
}
