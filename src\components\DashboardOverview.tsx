'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';

interface DashboardStats {
  products: number;
  brands: number;
  categories: number;
  banners: number;
}

export default function DashboardOverview() {
  const [stats, setStats] = useState<DashboardStats>({
    products: 0,
    brands: 0,
    categories: 0,
    banners: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [productsRes, brandsRes, categoriesRes, bannersRes] = await Promise.all([
          axios.get('/api/products', { withCredentials: true }),
          axios.get('/api/brands', { withCredentials: true }),
          axios.get('/api/categories', { withCredentials: true }),
          axios.get('/api/banners', { withCredentials: true })
        ]);

        setStats({
          products: productsRes.data.data?.length || 0,
          brands: brandsRes.data.data?.length || 0,
          categories: categoriesRes.data.data?.length || 0,
          banners: bannersRes.data.data?.length || 0
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      title: 'Total Products',
      value: stats.products,
      icon: '📦',
      color: 'from-blue-500 to-blue-600',
      href: '/admins/products'
    },
    {
      title: 'Total Brands',
      value: stats.brands,
      icon: '🏷️',
      color: 'from-green-500 to-green-600',
      href: '/admins/brands'
    },
    {
      title: 'Total Categories',
      value: stats.categories,
      icon: '📂',
      color: 'from-purple-500 to-purple-600',
      href: '/admins/categories'
    },
    {
      title: 'Total Banners',
      value: stats.banners,
      icon: '🖼️',
      color: 'from-yellow-500 to-yellow-600',
      href: '/admins/banners'
    }
  ];

  const quickActions = [
    {
      title: 'Add New Product',
      description: 'Create a new product in the catalog',
      icon: '➕',
      href: '/admins/products/new',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      title: 'Add New Brand',
      description: 'Register a new brand',
      icon: '🏷️',
      href: '/admins/brands/new',
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      title: 'Add New Category',
      description: 'Create a new product category',
      icon: '📂',
      href: '/admins/categories/new',
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      title: 'Add New Banner',
      description: 'Create a new promotional banner',
      icon: '🖼️',
      href: '/admins/banners/new',
      color: 'bg-yellow-500 hover:bg-yellow-600'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-yellow-600 to-yellow-500 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Welcome to GG Catalogs Admin</h1>
        <p className="text-yellow-100">Manage your store catalog, brands, categories, and promotional content.</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card) => (
          <a
            key={card.title}
            href={card.href}
            className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200"
          >
            <div className="flex items-center">
              <div className={`p-3 rounded-full bg-gradient-to-r ${card.color} text-white text-2xl`}>
                {card.icon}
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{card.title}</p>
                <p className="text-2xl font-bold text-gray-900">{card.value}</p>
              </div>
            </div>
          </a>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <a
              key={action.title}
              href={action.href}
              className={`${action.color} text-white p-4 rounded-lg transition-colors duration-200`}
            >
              <div className="text-2xl mb-2">{action.icon}</div>
              <h3 className="font-semibold mb-1">{action.title}</h3>
              <p className="text-sm opacity-90">{action.description}</p>
            </a>
          ))}
        </div>
      </div>

      {/* Recent Activity Placeholder */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div className="text-center py-8 text-gray-500">
          <p>Recent activity will be displayed here</p>
          <p className="text-sm mt-2">This feature will be implemented with webhooks for live updates</p>
        </div>
      </div>
    </div>
  );
}
